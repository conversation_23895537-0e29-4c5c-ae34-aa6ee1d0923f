import {StreamXmlParser, XmlParseTagEndChunk, XmlParseTagStartChunk, XmlParseTextChunk} from '../utils/string';
import {isToolName, ToolName} from '../tool';

interface TextChunk {
    type: 'text';
    content: string;
}

interface ThinkingStartChunk {
    type: 'thinkingStart';
    source: string;
}

interface ThinkingDeltaChunk {
    type: 'thinkingDelta';
    source: string;
}

interface ThinkingEndChunk {
    type: 'thinkingEnd';
    source: string;
}

interface TextInToolChunk {
    type: 'textInTool';
    source: string;
}

interface ToolStartChunk {
    type: 'toolStart';
    toolName: ToolName;
    source: string;
}

interface ToolDeltaChunk {
    type: 'toolDelta';
    arguments: Record<string, string>;
    source: string;
}

interface ToolEndChunk {
    type: 'toolEnd';
    source: string;
}

export type ToolParsedChunk =
    | TextChunk
    | ThinkingStartChunk
    | ThinkingDeltaChunk
    | ThinkingEndChunk
    | TextInToolChunk
    | ToolStartChunk
    | ToolDeltaChunk
    | ToolEndChunk;

export class StreamingToolParser {
    private tagStack: string[] = [];
    private completedToolCall: boolean = false;

    async *parse(stream: AsyncIterable<string>): AsyncIterable<ToolParsedChunk> {
        const parser = new StreamXmlParser();
        for await (const chunk of parser.parse(stream)) {
            if (this.completedToolCall) {
                break;
            }
            switch (chunk.type) {
                case 'text':
                    yield* this.yieldForTextChunk(chunk);
                    break;
                case 'tagStart':
                    yield* this.yieldForTagStart(chunk);
                    break;
                case 'tagEnd':
                    yield* this.yieldForTagEnd(chunk);
                    break;
            }
        }
    }

    *yieldForTextChunk(chunk: XmlParseTextChunk): Iterable<ToolParsedChunk> {
        const activeTag = this.tagStack.at(-1);
        // `<thinking>` tag has plain text inside it
        if (activeTag === 'think') {
            // 对于thinking标签内的文本，只输出一次，作为thinkingDelta
            yield {type: 'thinkingDelta', source: chunk.content};
        }
        // We don't allow text content in top level tag, all tool calls contains only parameters
        else if (activeTag) {
            if (this.tagStack.length > 1) {
                yield {type: 'toolDelta', arguments: {[activeTag]: chunk.content}, source: chunk.content};
            }
            else {
                yield {type: 'textInTool', source: chunk.content};
            }
        }
        else {
            // 对于标签外的文本，只输出一次，作为普通文本
            yield {type: 'text', content: chunk.content};
        }
    }

    *yieldForTagStart(chunk: XmlParseTagStartChunk): Iterable<ToolParsedChunk> {
        const activeTag = this.tagStack.at(-1);
        if (activeTag === 'think') {
            yield {type: 'thinkingDelta', source: chunk.source};
        }

        else if (activeTag) {
            if (isToolName(activeTag)) {
                // We're already in a tool call, a tag start means a start of parameter, just push a parameter name
                this.tagStack.push(chunk.tagName);
                yield {type: 'toolDelta', arguments: {[chunk.tagName]: ''}, source: chunk.source};
            }
            else {
                // We're already in a tool parameter not closed yet, see that not recursive param exist,
                // this should be part of content of the current param
                yield {type: 'toolDelta', arguments: {[activeTag]: chunk.content}, source: chunk.content};
            }
        }
        else if (chunk.tagName === 'think') {
            this.tagStack.push(chunk.tagName);
            yield {type: 'thinkingStart', source: chunk.source};
        }
        else if (isToolName(chunk.tagName)) {
            this.tagStack.push(chunk.tagName);
            yield {type: 'toolStart', toolName: chunk.tagName, source: chunk.source};
        }
        else {
            yield {type: 'text', content: chunk.source};
        }
    }

    *yieldForTagEnd(chunk: XmlParseTagEndChunk): Iterable<ToolParsedChunk> {
        if (!this.tagStack.length) {
            // 如果没有活动的标签，将结束标签作为普通文本处理
            yield {type: 'text', content: chunk.source};
            return;
        }

        const currentTag = this.tagStack.at(-1);
        if (currentTag === 'think') {
            if (chunk.tagName === 'think') {
                // 对于thinking标签的结束，只输出标签本身
                yield {type: 'thinkingEnd', source: chunk.source};
                this.tagStack.pop();
            }
            else {
                // 对于thinking标签内的其他结束标签，作为thinking内容处理
                yield {type: 'thinkingDelta', source: chunk.source};
            }
            return;
        }
        if (isToolName(currentTag)) {
            if (chunk.tagName === currentTag) {
                this.tagStack.pop();
                yield {type: 'toolEnd', source: chunk.source};
                this.completedToolCall = true;
            }
            else {
                yield {type: 'text', content: chunk.source};
            }
            return;
        }
        else {
            if (chunk.tagName === currentTag) {
                this.tagStack.pop();
                // 对于嵌套标签的结束，作为工具参数处理
                yield {type: 'toolDelta', arguments: {}, source: chunk.source};
            }
            else {
                yield {type: 'text', content: chunk.source};
            }
            return;
        }
    }
}
