import {assertNever} from '../utils/error';

export type EmbeddingContextMode = 'chunk' | 'fullContent' | 'nameOnly';

export interface CodePosition {
    line: number;
    column: number;
}

export interface EmbeddingSearchResultItem {
    file: string;
    start: CodePosition;
    end: CodePosition;
    content: string;
}

function renderChunkItem(item: EmbeddingSearchResultItem) {
    const lines = item.content.split('\n').slice(item.start.line, item.end.line + 1);

    if (!lines.length) {
        return '';
    }

    if (item.start.line === item.end.line) {
        lines[0] = lines[0].slice(item.start.column, item.end.column + 1);
    }
    else {
        lines[0] = lines[0].slice(item.start.column);
        lines[lines.length - 1] = lines[lines.length - 1].slice(0, item.end.column + 1);
    }

    const output = [
        `## ${item.file} line ${item.start.line}-${item.end.line}`,
        '',
        '```',
        lines.join('\n'),
        '```',
    ];
    return output.join('\n');
}

function asChunk(items: EmbeddingSearchResultItem[]) {
    const paragraphs = [
        '## Code Reference',
        'We already have some code related to user\'s query in this project which you can reference, they are **a part of** an existing code file, you are encouraged to read the entire file content again if you find one piece of code useful but does not contain enough information.',
        ...items.map(renderChunkItem).filter(v => !!v),
    ];
    return paragraphs.join('\n\n');
}

function renderFullContentItem(item: EmbeddingSearchResultItem) {
    const lines = [
        `## ${item.file}`,
        '',
        '```',
        item.content,
        '```',
    ];
    return lines.join('\n');
}

function asFullContent(items: EmbeddingSearchResultItem[]) {
    const paragraphs = [
        '## Code Reference',
        'We already have some files and their content related to user\'s query, you can trust these file content, however when you have already edited a file, the content is no longer in sync, you should read it again.',
        ...items.map(renderFullContentItem),
    ];
    return paragraphs.join('\n\n');
}

function asNameOnly(items: EmbeddingSearchResultItem[]) {
    const lines = [
        '## File Reference',
        '',
        'We already have some files related to user\'s query, their paths are provided below, you are encouraged to read their content if you think one file is useful.',
        ...items.map(v => `- ${v.file}`),
    ];
    return lines.join('\n');
}

export function renderEmbeddingSection(items: EmbeddingSearchResultItem[], mode: EmbeddingContextMode) {
    switch (mode) {
        case 'chunk':
            return asChunk(items);
        case 'fullContent':
            return asFullContent(items);
        case 'nameOnly':
            return asNameOnly(items);
        default:
            assertNever<string>(mode, v => `Unknown embedding context mode ${v}`);
    }
}
