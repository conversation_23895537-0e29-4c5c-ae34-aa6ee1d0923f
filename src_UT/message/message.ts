import {assertNever} from '../utils/error';
import {ChatInputPayload} from '../model';
import {now} from '../utils/string';
import {ModelToolCallInput, ModelToolCallInputWithSource, ToolName, ToolParsedChunk} from '../tool';

/** 文本块 */
export interface TextMessageChunk {
    type: 'text';
    content: string;
}

/** 纯文本块，和`TextMessageChunk`的区别是这个块不会按Markdown处理 */
export interface PlainTextMessageChunk {
    type: 'plainText';
    content: string;
}

/** 模块返回的工具调用块 */
export interface ToolCallMessageChunk {
    type: 'toolCall';
    toolName: ToolName;
    arguments: Record<string, string | undefined>;
    source: string;
}

/** 工具的思维链块 */
export interface ThinkingMessageChunk {
    type: 'thinking';
    content: string;
}

/** 可用于普通消息的块 */
export type MessageContentChunk = TextMessageChunk | ToolCallMessageChunk | ThinkingMessageChunk;

/** 用于调度消息的块 */
export type DebugContentChunk = TextMessageChunk | PlainTextMessageChunk;

function chunkToString(chunk: MessageContentChunk) {
    switch (chunk.type) {
        case 'text':
            return chunk.content;
        case 'thinking':
            return `<think>${chunk.content}</think>`;
        case 'toolCall':
            return chunk.source;
        default:
            assertNever<{type: string}>(chunk, v => `Unknown chunk type ${v.type}`);
    }
}

/** 一条消息的基础数据 */
interface MessageDataBase {
    uuid: string;
    createdAt: string;
    error?: string | undefined;
}

/** 调试级别 */
export type DebugMessageLevel = 'error' | 'warning' | 'info';

/** 调试消息的数据 */
export interface DebugMessageData extends MessageDataBase {
    type: 'debug';
    level: DebugMessageLevel;
    title: string;
    content: DebugContentChunk;
}

/** 用户发起的消息的数据 */
export interface UserRequestMessageData extends MessageDataBase {
    type: 'userRequest';
    content: string;
}

/** 模型返回的纯文本（无工具调用）消息的数据 */
export interface AssistantTextMessageData extends MessageDataBase {
    type: 'assistantText';
    chunks: MessageContentChunk[];
}

/** 模型返回的带有工具调用的消息的数据 */
export interface ToolCallMessageData extends MessageDataBase {
    type: 'toolCall';
    chunks: MessageContentChunk[];
}

/** 工具调用后返回给模型的消息数据 */
export interface ToolUseMessageData extends MessageDataBase {
    type: 'toolUse';
    content: string;
}

/** 全部消息的数据类型 */
export type MessageData =
    | DebugMessageData
    | UserRequestMessageData
    | AssistantTextMessageData
    | ToolCallMessageData
    | ToolUseMessageData;

export type MessageType = MessageData['type'];

export function isToolCallChunk(chunk: MessageContentChunk): chunk is ToolCallMessageChunk {
    return chunk.type === 'toolCall';
}

export function isReactiveToolCallChunk(chunk: MessageContentChunk) {
    return isToolCallChunk(chunk)
        && chunk.toolName !== 'ask_followup_question'
        && chunk.toolName !== 'attempt_completion';
}

export function isAssistantMessage(type: MessageType) {
    return type === 'assistantText' || type === 'toolCall';
}

abstract class MessageBase<T extends MessageType> {
    readonly uuid: string;

    readonly type: T;

    createdAt = now();

    error: string | undefined = undefined;

    constructor(uuid: string, type: T) {
        this.uuid = uuid;
        this.type = type;
    }

    setError(reason: string) {
        this.error = reason;
    }

    protected restore(persistData: MessageData) {
        this.createdAt = persistData.createdAt;
        this.error = persistData.error;
    }

    abstract toMessageData(): MessageData;

    abstract toChatInputPayload(): ChatInputPayload | null;

    protected toMessageDataBase(): MessageDataBase {
        return {
            uuid: this.uuid,
            createdAt: this.createdAt,
            error: this.error,
        };
    }
}

export class DebugMessage extends MessageBase<'debug'> {
    readonly level: DebugMessageLevel;

    readonly title: string;

    readonly content: DebugContentChunk;

    constructor(uuid: string, level: DebugMessageLevel, title: string, content: DebugContentChunk) {
        super(uuid, 'debug');
        this.level = level;
        this.title = title;
        this.content = content;
    }

    toMessageData(): DebugMessageData {
        return {
            ...this.toMessageDataBase(),
            type: this.type,
            level: this.level,
            title: this.title,
            content: this.content,
        };
    }

    // Debug message do not participate in chat
    toChatInputPayload(): ChatInputPayload | null {
        return null;
    }
}

export class UserRequestMessage extends MessageBase<'userRequest'> {
    static from(data: UserRequestMessageData) {
        const message = new UserRequestMessage(data.uuid, data.content);
        message.restore(data);
        return message;
    }

    content: string;

    constructor(uuid: string, content: string) {
        super(uuid, 'userRequest');
        this.content = content;
    }

    toMessageData(): UserRequestMessageData {
        return {
            ...this.toMessageDataBase(),
            type: this.type,
            content: this.content,
        };
    }

    toChatInputPayload(): ChatInputPayload {
        return {
            role: 'user',
            content: this.content,
        };
    }

    protected restore(persistData: UserRequestMessageData) {
        super.restore(persistData);
        this.content = persistData.content;
    }
}

abstract class AssistantMessage<T extends 'assistantText' | 'toolCall'> extends MessageBase<T> {
    protected readonly chunks: MessageContentChunk[] = [];

    // eslint-disable-next-line @typescript-eslint/no-useless-constructor
    constructor(uuid: string, type: T) {
        super(uuid, type);
    }

    getTextContent() {
        return this.chunks.map(chunkToString).join('');
    }

    toMessageData(): AssistantTextMessageData | ToolCallMessageData {
        return {
            ...this.toMessageDataBase(),
            type: this.type,
            chunks: this.chunks,
        };
    }

    toChatInputPayload(): ChatInputPayload {
        return {
            role: 'assistant',
            content: this.getModelVisibleTextContent(),
        };
    }

    protected restore(persistData: AssistantTextMessageData | ToolCallMessageData) {
        super.restore(persistData);
        this.chunks.push(...persistData.chunks);
    }

    private getModelVisibleTextContent() {
        return this.chunks.map(chunkToString).join('');
    }
}

export class ToolCallMessage extends AssistantMessage<'toolCall'> {
    static from(data: ToolCallMessageData) {
        const message = new ToolCallMessage(data);
        return message;
    }

    constructor(source: AssistantTextMessageData | ToolCallMessageData) {
        super(source.uuid, 'toolCall');
        this.restore(source);
    }

    getToolCallInput(): ModelToolCallInput {
        const toolCall = this.findToolCallChunkStrict();

        return {
            name: toolCall.toolName,
            arguments: toolCall.arguments,
        };
    }

    getToolCallInputWithSource(): ModelToolCallInputWithSource {
        const toolCall = this.findToolCallChunkStrict();

        return {
            name: toolCall.toolName,
            arguments: toolCall.arguments,
            source: toolCall.source,
        };
    }

    replaceToolCallInput(input: ModelToolCallInputWithSource) {
        const index = this.chunks.findIndex(isToolCallChunk);

        if (index < 0) {
            throw new Error('Invalid tool call message without tool chunk');
        }

        this.chunks[index] = {
            type: 'toolCall',
            toolName: input.name,
            arguments: input.arguments,
            source: input.source,
        };
    }

    toMessageData(): ToolCallMessageData {
        return {
            ...super.toMessageData(),
            type: this.type,
        };
    }

    private findToolCallChunkStrict() {
        const chunk = this.chunks.find(isToolCallChunk);

        if (!chunk) {
            throw new Error('Invalid tool call message without tool chunk');
        }

        return chunk;
    }
}

type MaybeChunk = MessageContentChunk | undefined;

function assertThinkingChunk(chunk: MaybeChunk, message: string): asserts chunk is ThinkingMessageChunk {
    if (chunk?.type !== 'thinking') {
        throw new Error(message);
    }
}

function assertToolCallChunk(chunk: MaybeChunk, message: string): asserts chunk is ToolCallMessageChunk {
    if (chunk?.type !== 'toolCall') {
        throw new Error(message);
    }
}

export class AssistantTextMessage extends AssistantMessage<'assistantText'> {
    static from(data: AssistantTextMessageData) {
        const message = new AssistantTextMessage(data.uuid);
        message.restore(data);
        return message;
    }

    private completedToolCall: boolean = false;

    constructor(uuid: string) {
        super(uuid, 'assistantText');
    }

    addChunk(chunk: ToolParsedChunk) {
        if (this.completedToolCall) {
            return;
        }

        if (chunk.type === 'text') {
            const lastChunk = this.chunks.at(-1);
            if (lastChunk?.type === 'text') {
                lastChunk.content += chunk.content;
            }
            else {
                this.chunks.push({type: 'text', content: chunk.content});
            }
            return;
        }

        const lastChunk = this.chunks.at(-1);

        if (chunk.type === 'thinkingStart') {
            this.chunks.push({type: 'thinking', content: ''});
        }
        else if (chunk.type === 'toolStart') {
            const toolChunk: ToolCallMessageChunk = {
                type: 'toolCall',
                toolName: chunk.toolName,
                arguments: {},
                source: chunk.source,
            };
            this.chunks.push(toolChunk);
        }
        else if (chunk.type === 'thinkingDelta') {
            assertThinkingChunk(lastChunk, 'Unexpected thinking delta chunk coming without a start chunk');
            lastChunk.content += chunk.source;
        }
        else if (chunk.type === 'thinkingEnd') {
            assertThinkingChunk(lastChunk, 'Unexpected thinking end chunk coming without a start chunk');
        }
        else if (chunk.type === 'toolDelta') {
            assertToolCallChunk(lastChunk, 'Unexpected tool delta chunk coming without a start chunk');
            lastChunk.source += chunk.source;
            for (const [key, value] of Object.entries(chunk.arguments)) {
                const previousValue = lastChunk.arguments[key] ?? '';
                lastChunk.arguments[key] = previousValue + value;
            }
        }
        else if (chunk.type === 'toolEnd') {
            assertToolCallChunk(lastChunk, 'Unexpected tool end chunk coming without a start chunk');
            lastChunk.source += chunk.source;
            this.completedToolCall = true;
            return;
        }
        else if (chunk.type === 'textInTool') {
            assertToolCallChunk(lastChunk, 'Unexpected tool end chunk coming without a start chunk');
            lastChunk.source += chunk.source;
        }
        else {
            assertNever<{type: string}>(chunk, v => `Unexpected chunk type: ${v.type}`);
        }
    }

    toMessageData(): AssistantTextMessageData {
        return {
            ...super.toMessageData(),
            type: this.type,
        };
    }

    toToolCallMessage(): ToolCallMessage | null {
        if (this.chunks.some(isReactiveToolCallChunk)) {
            const toolCallChunks = this.chunks.slice(0, this.chunks.findIndex(isReactiveToolCallChunk) + 1);
            const toolCallMessageData: ToolCallMessageData = {
                ...this.toMessageDataBase(),
                type: 'toolCall',
                chunks: toolCallChunks,
            };
            return new ToolCallMessage(toolCallMessageData);
        }
        return null;
    }
}

export class ToolUseMessage extends MessageBase<'toolUse'> {
    static from(data: ToolUseMessageData) {
        const message = new ToolUseMessage(data.uuid, data.content);
        message.restore(data);
        return message;
    }

    content: string;

    constructor(uuid: string, content: string) {
        super(uuid, 'toolUse');

        this.content = content;
    }

    toMessageData(): MessageData {
        return {
            ...this.toMessageDataBase(),
            type: this.type,
            content: this.content,
        };
    }

    toChatInputPayload(): ChatInputPayload {
        return {
            role: 'user',
            content: this.content,
        };
    }

    protected restore(persistData: ToolUseMessageData) {
        super.restore(persistData);
        this.content = persistData.content;
    }
}

export type Message = DebugMessage | UserRequestMessage | AssistantTextMessage | ToolCallMessage | ToolUseMessage;

export function deserializeMessage(data: MessageData): Message {
    switch (data.type) {
        case 'userRequest':
            return UserRequestMessage.from(data);
        case 'assistantText':
            return AssistantTextMessage.from(data);
        case 'toolCall':
            return ToolCallMessage.from(data);
        case 'toolUse':
            return ToolUseMessage.from(data);
        case 'debug':
            throw new Error('Unexpected debug message on deserialization');
        default:
            assertNever<{type: string}>(data, v => `Unknown message type ${v.type}`);
    }
}
