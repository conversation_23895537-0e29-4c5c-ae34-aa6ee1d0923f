
import {stringifyError} from '../utils/error';
import {Message, ThreadStore, Workflow, WorkflowOriginMessage} from '../message';

export interface WorkflowRunnerInit {
    threadUuid: string;
    store: ThreadStore;
    base: Message[];
    origin: WorkflowOriginMessage;
    workflow: Workflow;
}

export interface WorkflowRunResult {
    finished: boolean;
    lastToolError?: string;
}

export abstract class WorkflowRunner {
    protected readonly threadUuid: string;
    protected readonly base: Message[];
    protected readonly origin: WorkflowOriginMessage;
    protected readonly workflow: Workflow;
    private lastToolStatus: {name: string; success: boolean} | null = null;

    constructor(init: WorkflowRunnerInit) {
        this.threadUuid = init.threadUuid;
        this.base = init.base;
        this.origin = init.origin;
        this.workflow = init.workflow;
    }

    getWorkflow() {
        return this.workflow;
    }

    async run(): Promise<void> {
        try {
            console.log('[Workflow] Starting execution for thread:', this.threadUuid);
            const result = await this.execute();
            
            if (result.lastToolError) {
                console.error('[Workflow] Last tool failed:', result.lastToolError);
            }
            
            this.workflow.setContinueRoundtrip(!result.finished);
            console.log('[Workflow] Execution completed. Continue roundtrip:', 
                      !result.finished, 'Reason:', result.lastToolError || 'Normal completion');
        }
        catch (ex) {
            console.error('[Workflow] Critical workflow error:', stringifyError(ex));
            this.origin.setError(stringifyError(ex));
            this.workflow.setContinueRoundtrip(false);
        }
    }

    protected setLastToolStatus(name: string, success: boolean) {
        this.lastToolStatus = {name, success};
        console.log(`[Workflow] Tool ${name} executed ${success ? 'successfully' : 'with errors'}`);
    }

    protected abstract execute(): Promise<WorkflowRunResult>;
}
